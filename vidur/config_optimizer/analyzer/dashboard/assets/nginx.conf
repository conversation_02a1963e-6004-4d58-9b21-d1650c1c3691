events {
    use epoll;
    accept_mutex on;
}

http {
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }
    gzip on;
    gzip_comp_level 2;
    gzip_min_length 512;
    server_tokens off;
    log_format l2met 'measure#nginx.service=$request_time request_id=$http_x_request_id';
    include mime.types;
    default_type application/octet-stream;
    sendfile on;
    client_body_timeout 5;

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name vidur.westus2.cloudapp.azure.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl;
        server_name vidur.westus2.cloudapp.azure.com;

        # SSL configuration
        ssl_certificate /etc/letsencrypt/live/vidur.westus2.cloudapp.azure.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/vidur.westus2.cloudapp.azure.com/privkey.pem;
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout  10m;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;

        location / {
                    proxy_pass http://127.0.0.1:8501/;
                    proxy_set_header        Host $host;
                    proxy_set_header        X-Real-IP $remote_addr;
                    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
                    proxy_set_header        X-Forwarded-Proto $scheme;
                    proxy_http_version 1.1;
                    proxy_set_header Upgrade $http_upgrade;
                    proxy_set_header Connection "upgrade";
        }

        location /_stcore/stream {
                proxy_pass http://127.0.0.1:8501/_stcore/stream;
                proxy_http_version 1.1;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header Host $host;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
                proxy_read_timeout 86400;
        }
    }
}
