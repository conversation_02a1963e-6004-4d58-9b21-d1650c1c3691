# vLLM vs Vidur 性能对比分析报告

**分析时间**: 2025-08-19 08:04:53

## 📊 **对比结果摘要**

### 数据源
- **vLLM 结果文件**: `vllm_results_20250819_080001_qps4.csv`
- **Vidur 结果文件**: `request_metrics.csv`

### 基本统计
- **总记录数**: 995 条
- **vLLM 平均 e2e 时间**: 44.282s
- **Vidur 平均 e2e 时间**: 3.592s
- **整体平均差异**: -40.690s (-91.9%)

### 关键发现
- **平均绝对相对差异**: 91.9%
- **相对差异标准差**: 4.6%
- **差异范围**: -100.0% 到 -80.0%

## 🎯 **保真度评估**

| 差异范围 | 记录数 | 占比 | 评级 |
|---------|--------|------|---------|
| < 5% | 0 条 | 0.0% | 优秀 |
| 5-10% | 0 条 | 0.0% | 良好 |
| 10-20% | 0 条 | 0.0% | 一般 |
| ≥ 20% | 995 条 | 100.0% | 较差 |

## 📈 **差异分布分析**

### 分位数分析
- **P50**: 91.6%
- **P75**: 95.2%
- **P90**: 99.0%
- **P95**: 99.7%
- **P99**: 100.0%

### 主要观察
1. **系统性低估**: Vidur 系统性地低估了执行时间（平均低 91.9%）
2. **差异稳定**: 标准差 4.6%，说明偏差相对稳定
3. **影响范围**: 100.0% 的请求差异超过 20%

## 💡 **结论**

⚠️ **Vidur 的保真度有待改进**，平均差异 91.9% 超出理想范围。

---
*报告生成时间: 2025-08-19 08:04:53*
