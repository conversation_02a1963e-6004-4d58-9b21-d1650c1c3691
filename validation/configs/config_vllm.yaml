# vLLM 特有配置文件 - config_vllm.yaml
# 包含 vLLM 服务器特有的参数

# 模型路径（本地路径）
model: "/mnt/yrfs/llm_weights/Meta-Llama-3.1-8B"

# 从共享配置继承的关键参数（用于验证一致性）
max_num_seqs: 128  # 对应共享配置的 max_num_seqs
block_size: 16  # 对应共享配置的 block_size
tensor_parallel_size: 1  # 对应共享配置的 tensor_parallel_size
pipeline_parallel_size: 1  # 对应共享配置的 pipeline_parallel_size
max_model_len: 4096  # 对应共享配置的 max_tokens
gpu_memory_utilization: 0.8  # 对应共享配置的 gpu_memory_utilization，降低以避免内存不足

# 服务器配置
host: "localhost"
port: 8002
api_server_count: 1

# 前端配置
disable_log_requests: false
disable_log_stats: false
disable_frontend_multiprocessing: false
enable_auto_tool_choice: false
enable_request_id_headers: false
disable_fastapi_docs: false

# 模型配置
tokenizer_mode: "auto"
revision: null
code_revision: null
tokenizer_revision: null
skip_tokenizer_init: false
served_model_name: null
config_format: "auto"
hf_token: null
hf_overrides: {}

# 量化配置
quantization: null
enforce_eager: true  # 禁用 CUDA Graph 以与 Vidur 保持一致
max_seq_len_to_capture: 8192

# Attention Backend 配置
attention_backend: "FLASHINFER"  # 使用 FlashInfer 作为 attention backend

# 加载配置
load_format: "auto"
download_dir: null
ignore_patterns: null
use_tqdm_on_load: true

# 缓存配置
swap_space: 4
cpu_offload_gb: 0
enable_prefix_caching: false  # 明确禁用 prefix caching 以与 Vidur 保持一致
prefix_caching_hash_algo: "builtin"
num_gpu_blocks_override: null
calculate_kv_scales: false

# 多模态配置
limit_mm_per_prompt: {}
media_io_kwargs: {}
mm_processor_kwargs: null
disable_mm_preprocessor_cache: false
interleave_mm_strings: false

# LoRA 配置
enable_lora: null
enable_lora_bias: false
max_loras: 1
max_lora_rank: 16
lora_extra_vocab_size: 256
lora_dtype: "auto"
max_cpu_loras: null
fully_sharded_loras: false

# 并行配置
distributed_executor_backend: null
data_parallel_size: 1
data_parallel_rank: null
data_parallel_start_rank: null
data_parallel_size_local: null
data_parallel_address: null
data_parallel_rpc_port: null
data_parallel_backend: "mp"
data_parallel_hybrid_lb: false
enable_expert_parallel: false
enable_eplb: false
num_redundant_experts: 0
eplb_window_size: 1000
eplb_step_interval: 3000
eplb_log_balancedness: false
max_parallel_loading_workers: null
ray_workers_use_nsight: false
disable_custom_all_reduce: false
worker_cls: "auto"
worker_extension_cls: ""
enable_multimodal_encoder_data_parallel: false

# 调度配置
preemption_mode: null
num_scheduler_steps: 1
multi_step_stream_outputs: true
scheduling_policy: "fcfs"
enable_chunked_prefill: null
disable_chunked_mm_input: false
scheduler_cls: "vllm.core.scheduler.Scheduler"
disable_hybrid_kv_cache_manager: false
async_scheduling: false
max_num_partial_prefills: 1
max_long_partial_prefills: 1
long_prefill_token_threshold: 0
num_lookahead_slots: 0
scheduler_delay_factor: 0.0
cuda_graph_sizes: []

# 解码配置
guided_decoding_backend: "auto"
guided_decoding_disable_fallback: false
guided_decoding_disable_any_whitespace: false
guided_decoding_disable_additional_properties: false

# 观测配置
otlp_traces_endpoint: null
collect_detailed_traces: null
show_hidden_metrics_for_version: null

# 推测解码配置
speculative_config: null

# 编译配置
compilation_config:
  level: 0
  debug_dump_path: ""
  cache_dir: ""
  backend: ""
  custom_ops: []
  splitting_ops: []
  use_inductor: true
  compile_sizes: null
  inductor_compile_config:
    enable_auto_functionalized_v2: false
  inductor_passes: {}
  use_cudagraph: false  # 禁用编译时 CUDA Graph
  cudagraph_num_of_warmups: 0
  cudagraph_capture_sizes: null
  cudagraph_copy_inputs: false
  full_cuda_graph: false
  max_capture_size: null
  local_cache_dir: null

# KV 传输和事件配置
kv_transfer_config: null
kv_events_config: null

# 客户端请求参数
request_params:
  temperature: 1.0
  top_p: 0.9
  stream: false

# 网络超时设置（秒）
timeout_settings:
  total_timeout: 300  # 5 分钟超时，因为LLM推理可能需要较长时间
  model_list_timeout: 30  # 获取模型列表的超时时间

# 基准测试参数 - 从共享配置获取关键参数
benchmark:
  prompts_file: "validation/datasets/context_values_unique.json"
  # prompts_file: "validation/datasets/bbc-news-summary.json"
  max_tokens: 1024  # 这是输出 token 数，不是总 token 数
  output_dir: "validation/results/vllm"

# 日志配置
logging:
  log_dir: "validation/logs/client"

# # 配置文件路径
# config_paths:
#   shared_config: "validation/configs/config_shared.yaml"
#   vllm_config: "validation/configs/config_vllm.yaml"

# 测试数据集配置
test_dataset: "PLACEHOLDER_VLLM_DATASET"  # 用于基准测试的数据集

# 其他配置
additional_config: {}
headless: false
