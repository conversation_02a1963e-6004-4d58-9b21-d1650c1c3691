#!/usr/bin/env python3
"""
vLLM API 客户端脚本
通过 HTTP API 访问 vLLM 服务器，收集性能数据用于与 Vidur 对比
"""

import json
import time
import csv
import asyncio
import aiohttp
from datetime import datetime
from pathlib import Path
import argparse
import yaml
import logging
import sys
import traceback
from typing import List, Dict, Any
from tqdm import tqdm


def setup_logging(log_dir: str) -> logging.Logger:
    """设置日志系统"""
    # 创建日志目录
    Path(log_dir).mkdir(parents=True, exist_ok=True)
    
    # 生成带时间戳的日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f"{log_dir}/vllm_client_{timestamp}.log"
    
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 创建 logger
    logger = logging.getLogger('vllm_client')
    logger.setLevel(logging.INFO)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 文件处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(log_format, date_format)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s', date_format)
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    logger.info(f"日志系统初始化完成，日志文件: {log_file}")
    return logger


def load_config(config_path: str) -> Dict[str, Any]:
    """加载 YAML 配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def merge_configs(shared_config: Dict[str, Any], specific_config: Dict[str, Any]) -> Dict[str, Any]:
    """合并共享配置和特定配置"""
    merged = shared_config.copy()
    merged.update(specific_config)
    return merged


async def send_request(session: aiohttp.ClientSession, url: str, prompt: str, max_tokens: int, start_time: float, model_name: str, logger: logging.Logger, arrived_at: float, request_params: Dict[str, Any]) -> Dict[str, Any]:
    """发送单个请求到 vLLM 服务器"""
    payload = {
        "model": model_name,
        "prompt": prompt,
        "max_tokens": max_tokens,
        "temperature": request_params.get('temperature', 0.5),
        "top_p": request_params.get('top_p', 0.9),
        "stream": request_params.get('stream', False)
    }
    
    # 记录请求发送时间
    request_start_time = time.time()
    
    logger.debug(f"发送请求: prompt长度={len(prompt)}, arrived_at={arrived_at:.3f}")
    
    try:
        async with session.post(url, json=payload) as response:
            if response.status == 200:
                result = await response.json()
                request_end_time = time.time()
                
                # 提取响应信息
                choice = result['choices'][0]
                message = choice['text']
                usage = result['usage']
                e2e_time = request_end_time - request_start_time
                
                logger.info(f"请求成功 (arrived_at={arrived_at:.3f}): "
                          f"request_e2e_time={e2e_time:.3f}s, "
                          f"输入tokens={usage['prompt_tokens']}, "
                          f"输出tokens={usage['completion_tokens']}")
                
                result_data = {
                    'arrived_at': arrived_at,
                    'num_prefill_tokens': usage['prompt_tokens'],
                    'num_decode_tokens': usage['completion_tokens'],
                    'total_tokens': usage['total_tokens'],
                    'response': message,
                    'request_e2e_time': e2e_time,  # 对齐 Vidur 的列名
                    'request_e2e_time_normalized': e2e_time / usage['completion_tokens'] if usage['completion_tokens'] > 0 else 0,
                    'success': True
                }
                
                return result_data
            else:
                error_text = await response.text()
                logger.error(f"请求失败 (arrived_at={arrived_at:.3f}): HTTP {response.status} - {error_text}")
                return {
                    'arrived_at': arrived_at,
                    'success': False,
                    'error': f"HTTP {response.status}"
                }
    except Exception as e:
        # 获取更详细的异常信息
        error_type = type(e).__name__
        error_msg = str(e) if str(e) else "未知异常"

        # 特殊处理一些常见的异常类型
        if hasattr(e, 'errno'):
            error_msg += f" (errno: {e.errno})"
        if hasattr(e, 'code'):
            error_msg += f" (code: {e.code})"

        logger.error(f"请求异常 (arrived_at={arrived_at:.3f}): {error_type} - {error_msg}")
        return {
            'arrived_at': arrived_at,
            'success': False,
            'error': f"{error_type}: {error_msg}"
        }


async def run_benchmark(prompts_file: str, server_url: str, max_tokens: int = 128, qps: float = 1.0, output_dir: str = "validation/results", model_name: str = None, logger: logging.Logger = None, config: Dict[str, Any] = None):
    """运行基准测试"""
    if logger is None:
        logger = logging.getLogger('vllm_client')
    
    # 加载 prompts
    logger.info(f"加载 prompts 文件: {prompts_file}")
    with open(prompts_file, 'r', encoding='utf-8') as f:
        prompts = json.load(f)
    
    # 如果没有指定模型名称，自动获取
    if not model_name:
        logger.info("正在获取可用模型...")
        default_model = config.get('default_model_name', '/share_data/llm_weights/Meta-Llama-3-8B')
        timeout_settings = config.get('timeout_settings', {})
        model_timeout = timeout_settings.get('model_list_timeout', 30)
        timeout = aiohttp.ClientTimeout(total=model_timeout)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            models_url = server_url.replace('/v1/completions', '/v1/models')
            try:
                async with session.get(models_url) as response:
                    if response.status == 200:
                        models_data = await response.json()
                        if models_data.get('data'):
                            model_name = models_data['data'][0]['id']
                            logger.info(f"自动选择模型: {model_name}")
                        else:
                            model_name = default_model
                            logger.warning(f"未找到可用模型，使用默认值: {model_name}")
                    else:
                        model_name = default_model
                        logger.warning(f"获取模型列表失败 (HTTP {response.status})，使用默认值: {model_name}")
            except Exception as e:
                logger.error(f"获取模型列表异常: {e}")
                model_name = default_model
                logger.info(f"使用默认模型: {model_name}")
    
    # 检查是否启用 static workflow 模式
    static_workflow = config.get('static_workflow', False)

    logger.info(f"基准测试配置:")
    logger.info(f"  - prompts 数量: {len(prompts)}")
    logger.info(f"  - Static Workflow 模式: {static_workflow}")
    if static_workflow:
        logger.info(f"  - 模式: 批处理模式（所有请求同时发送）")
        interval = 0  # 无间隔
    else:
        logger.info(f"  - 目标 QPS: {qps}")
        # 计算请求间隔
        interval = 1.0 / qps if qps > 0 else 0
        logger.info(f"  - 请求间隔: {interval:.3f} 秒")
    logger.info(f"  - 服务器地址: {server_url}")
    logger.info(f"  - 最大输出令牌数: {max_tokens}")
    logger.info(f"  - 使用模型: {model_name}")
    
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 记录开始时间
    start_time = time.time()
    logger.info(f"开始发送请求，测试开始时间: {datetime.fromtimestamp(start_time)}")
    
    # 创建 HTTP 会话
    timeout_settings = config.get('timeout_settings', {})
    total_timeout = timeout_settings.get('total_timeout', 300)
    timeout = aiohttp.ClientTimeout(total=total_timeout)
    async with aiohttp.ClientSession(timeout=timeout) as session:
        # 用于存储正在进行的任务
        active_tasks = []
        results = []
        
        # 使用进度条显示发送进度
        with tqdm(total=len(prompts), desc="Sending requests") as pbar:
            if static_workflow:
                # Static workflow 模式：同时发送所有请求
                logger.info("Static workflow 模式：同时发送所有请求...")
                for i, prompt in enumerate(prompts):
                    # 在 static workflow 模式下，所有请求的 arrived_at 都是 0
                    arrived_at = 0.0

                    # 创建并启动请求任务
                    request_params = config.get('request_params', {})
                    task = asyncio.create_task(
                        send_request(session, server_url, prompt, max_tokens, start_time, model_name, logger, arrived_at, request_params)
                    )
                    active_tasks.append((task, i))

                    # 更新进度条
                    pbar.update(1)
            else:
                # 正常模式：按 QPS 控制发送间隔
                for i, prompt in enumerate(prompts):
                    # 计算当前请求应该发送的时间
                    scheduled_time = start_time + i * interval
                    current_time = time.time()

                    # 如果还没到发送时间，则等待
                    if current_time < scheduled_time:
                        await asyncio.sleep(scheduled_time - current_time)

                    # 计算实际的到达时间（相对于开始时间）
                    arrived_at = time.time() - start_time

                    # 创建并启动请求任务
                    request_params = config.get('request_params', {})
                    task = asyncio.create_task(
                        send_request(session, server_url, prompt, max_tokens, start_time, model_name, logger, arrived_at, request_params)
                    )
                    active_tasks.append((task, i))

                    # 更新进度条
                    pbar.update(1)
                
                # 检查并收集已完成的任务结果
                # 这样可以避免内存中积累过多的任务
                completed_tasks = []
                for task, task_id in active_tasks:
                    if task.done():
                        try:
                            result = task.result()
                            results.append(result)
                        except Exception as e:
                            logger.error(f"任务异常: {e}")
                            results.append({
                                'arrived_at': arrived_at,
                                'success': False,
                                'error': str(e)
                            })
                        completed_tasks.append((task, task_id))
                
                # 从活跃任务列表中移除已完成的任务
                for completed_task in completed_tasks:
                    active_tasks.remove(completed_task)
        
        # 等待所有剩余的任务完成
        logger.info("等待所有剩余请求完成...")
        remaining_results = await asyncio.gather(*[task for task, _ in active_tasks], return_exceptions=True)
        
        # 处理剩余的结果
        for i, result in enumerate(remaining_results):
            if isinstance(result, Exception):
                logger.error(f"任务异常: {result}")
                results.append({
                    'arrived_at': 0,  # 无法准确确定到达时间
                    'success': False,
                    'error': str(result)
                })
            else:
                results.append(result)
    
    # 处理结果
    successful_results = []
    failed_count = 0
    filtered_count = 0

    for result in results:
        if result.get('success', False):
            successful_results.append(result)
        else:
            failed_count += 1

    logger.info(f"测试完成统计:")
    logger.info(f"  - 成功请求: {len(successful_results)}")
    logger.info(f"  - 失败请求: {failed_count}")

    if not successful_results:
        logger.warning("没有成功的请求，无法生成结果文件")
        return
    
    # 按 arrived_at 时间排序
    successful_results_sorted = sorted(successful_results, key=lambda x: x['arrived_at'])

    # 时间处理：根据模式进行不同的处理
    if static_workflow:
        # Static workflow 模式：所有请求的 arrived_at 都应该是 0
        logger.info("Static workflow 模式: 所有请求的 arrived_at 设置为 0")
        for result in successful_results_sorted:
            result['arrived_at'] = 0.0
    else:
        # 正常模式：时间平移，将最小的 arrived_at 设置为 0
        if successful_results_sorted:
            min_arrived_at = successful_results_sorted[0]['arrived_at']
            logger.info(f"时间平移: 最小 arrived_at = {min_arrived_at:.6f}s，将其设置为 0")

            for result in successful_results_sorted:
                result['arrived_at'] -= min_arrived_at

            logger.info(f"时间平移后: arrived_at 范围 = [0, {successful_results_sorted[-1]['arrived_at']:.6f}]s")

    # 分配 request_id
    for i, result in enumerate(successful_results_sorted):
        result['request_id'] = i
    
    # 保存结果 - 包含模式信息
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if static_workflow:
        # Static workflow 模式
        mode_suffix = "static"
        csv_path = f"{output_dir}/vllm_results_{timestamp}_{mode_suffix}.csv"
    else:
        # 正常模式 - 包含QPS信息
        qps_str = f"{qps:g}"
        csv_path = f"{output_dir}/vllm_results_{timestamp}_qps{qps_str}.csv"
    
    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        # 使用与 Vidur 一致的列名
        fieldnames = [
            'request_id',
            'arrived_at',
            'num_prefill_tokens',
            'num_decode_tokens',
            'request_e2e_time',  # 对齐 Vidur 的列名
            'request_e2e_time_normalized'
        ]

        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for result in successful_results_sorted:
            row_data = {
                'request_id': result['request_id'],
                'arrived_at': result['arrived_at'],
                'num_prefill_tokens': result['num_prefill_tokens'],
                'num_decode_tokens': result['num_decode_tokens'],
                'request_e2e_time': result['request_e2e_time'],
                'request_e2e_time_normalized': result['request_e2e_time_normalized'],
            }
            writer.writerow(row_data)
    
    # 保存详细结果 - 包含模式信息
    if static_workflow:
        json_path = f"{output_dir}/vllm_detailed_results_{timestamp}_{mode_suffix}.json"
    else:
        json_path = f"{output_dir}/vllm_detailed_results_{timestamp}_qps{qps_str}.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(successful_results_sorted, f, indent=2, ensure_ascii=False)
    
    # 计算统计信息
    total_tokens = sum(result['num_prefill_tokens'] + result['num_decode_tokens'] for result in successful_results_sorted)
    avg_e2e_time = sum(result['request_e2e_time'] for result in successful_results_sorted) / len(successful_results_sorted)

    if static_workflow:
        # Static workflow 模式：所有请求同时到达，时间统计基于最长的 e2e 时间
        max_e2e_time = max(result['request_e2e_time'] for result in successful_results_sorted) if successful_results_sorted else 0
        total_time = max_e2e_time  # 总时间等于最长的请求处理时间
        throughput = total_tokens / total_time if total_time > 0 else 0
        actual_qps = len(successful_results_sorted) / total_time if total_time > 0 else 0
    else:
        # 正常模式：基于 arrived_at 时间计算
        total_time = max(result['arrived_at'] for result in successful_results_sorted) if successful_results_sorted else 0
        throughput = total_tokens / total_time if total_time > 0 else 0
        actual_qps = len(successful_results_sorted) / total_time if total_time > 0 else 0
    
    logger.info(f"性能统计:")
    if static_workflow:
        logger.info(f"  - 模式: Static Workflow (批处理)")
        logger.info(f"  - 总处理时间: {total_time:.2f} 秒 (最长请求的 e2e 时间)")
    else:
        logger.info(f"  - 模式: 正常模式 (QPS 控制)")
        logger.info(f"  - 总时间: {total_time:.2f} 秒")
    logger.info(f"  - 总令牌数: {total_tokens}")
    logger.info(f"  - 平均端到端时间 (request_e2e_time): {avg_e2e_time:.3f} 秒")
    logger.info(f"  - 吞吐量: {throughput:.2f} tokens/sec")
    logger.info(f"  - 实际 QPS: {actual_qps:.2f}")
    
    # 如果有细粒度时间数据，也显示统计
    has_prefill_time = any('prefill_time' in result for result in successful_results_sorted)
    has_decode_time = any('decode_time' in result for result in successful_results_sorted)

    if has_prefill_time:
        prefill_times = [result['prefill_time'] for result in successful_results_sorted if 'prefill_time' in result]
        avg_prefill_time = sum(prefill_times) / len(prefill_times)
        logger.info(f"  - 平均Prefill时间: {avg_prefill_time:.3f} 秒")

    if has_decode_time:
        decode_times = [result['decode_time'] for result in successful_results_sorted if 'decode_time' in result]
        avg_decode_time = sum(decode_times) / len(decode_times)
        logger.info(f"  - 平均Decode时间: {avg_decode_time:.3f} 秒")

    # 显示数据质量统计
    decode_token_stats = [result['num_decode_tokens'] for result in successful_results_sorted]
    min_decode = min(decode_token_stats)
    max_decode = max(decode_token_stats)
    avg_decode = sum(decode_token_stats) / len(decode_token_stats)
    logger.info(f"  - Decode tokens 统计: min={min_decode}, max={max_decode}, avg={avg_decode:.1f}")
    
    logger.info(f"文件输出:")
    logger.info(f"  - CSV 结果: {csv_path}")
    logger.info(f"  - 详细结果: {json_path}")
    logger.info(f"请将 CSV 文件路径更新到 config_vidur.yaml 的 trace_file 字段中")


def main():
    parser = argparse.ArgumentParser(description="vLLM API 基准测试客户端")
    parser.add_argument("--prompts-file", 
                       help="Prompts 文件路径")
    parser.add_argument("--server-url", 
                       help="vLLM 服务器 API 地址")
    parser.add_argument("--max-tokens", type=int, 
                       help="最大输出令牌数")
    parser.add_argument("--qps", type=float, 
                       help="每秒请求数")
    parser.add_argument("--output-dir", 
                       help="输出目录")
    parser.add_argument("--shared-config", 
                       help="共享配置文件路径")
    parser.add_argument("--vllm-config", 
                       help="vLLM 配置文件路径")
    
    args = parser.parse_args()
    
    # 从配置文件读取参数
    try:
        # 使用配置文件中的默认路径，或命令行指定的路径
        shared_config_path = args.shared_config or "validation/configs/config_shared.yaml"
        vllm_config_path = args.vllm_config or "validation/configs/config_vllm.yaml"
        
        shared_config = load_config(shared_config_path)
        vllm_config = load_config(vllm_config_path)
        merged_config = merge_configs(shared_config, vllm_config)
        
    except Exception as e:
        print(f"错误: 无法读取配置文件: {e}")
        print(f"请确保配置文件存在:")
        print(f"  - {shared_config_path}")
        print(f"  - {vllm_config_path}")
        sys.exit(1)
    
    # 验证必需的配置参数
    required_configs = {
        'logging': ['log_dir'],
        'benchmark': ['max_tokens', 'prompts_file', 'output_dir'],
        'request_params': ['temperature', 'top_p', 'stream'],
        'timeout_settings': ['total_timeout', 'model_list_timeout'],
        'model_name': None,  # 顶级参数
        'host': None,  # 顶级参数
        'port': None,  # 顶级参数
        'qps': None,  # 顶级参数
    }
    
    missing_configs = []
    for section, keys in required_configs.items():
        if keys is None:  # 顶级参数
            if section not in merged_config:
                missing_configs.append(section)
        else:  # 嵌套参数
            if section not in merged_config:
                missing_configs.append(f"{section} (整个配置段)")
            else:
                for key in keys:
                    if key not in merged_config[section]:
                        missing_configs.append(f"{section}.{key}")
    
    if missing_configs:
        print("错误: 配置文件中缺少必需的参数:")
        for config in missing_configs:
            print(f"  - {config}")
        print("\n请检查并更新配置文件中的所有必需参数")
        sys.exit(1)
    
    # 设置日志系统（使用配置文件中的日志目录）
    log_config = merged_config['logging']
    log_dir = log_config['log_dir']
    logger = setup_logging(log_dir)
    
    logger.info(f"启动 vLLM 客户端，参数: {vars(args)}")
    
    # 使用配置文件中的参数，命令行参数可以覆盖
    benchmark_config = merged_config['benchmark']
    
    qps = args.qps if args.qps is not None else merged_config['qps']
    max_tokens = args.max_tokens if args.max_tokens is not None else benchmark_config['max_tokens']
    prompts_file = args.prompts_file if args.prompts_file else benchmark_config['prompts_file']
    output_dir = args.output_dir if args.output_dir else benchmark_config['output_dir']
    
    port = merged_config['port']
    host = merged_config['host']
    
    server_url = args.server_url if args.server_url else f"http://{host}:{port}/v1/completions"
    
    logger.info(f"使用配置参数: QPS={qps}, max_tokens={max_tokens}, host={host}, port={port}")
    logger.info(f"配置文件: shared={shared_config_path}, vllm={vllm_config_path}")
    
    # 检查 prompts 文件
    if not Path(prompts_file).exists():
        logger.error(f"Prompts 文件不存在: {prompts_file}")
        logger.info("请先运行: python validation/data_scripts/generate_vllm_test_data.py")
        return
    
    # 运行基准测试
    try:
        asyncio.run(run_benchmark(
            prompts_file=prompts_file,
            server_url=server_url,
            max_tokens=max_tokens,
            qps=qps,
            output_dir=output_dir,
            model_name=None,  # 自动获取模型名称
            logger=logger,
            config=merged_config
        ))
        logger.info("基准测试完成")
    except Exception as e:
        logger.error(f"基准测试异常: {e}", exc_info=True)


if __name__ == "__main__":
    main()
