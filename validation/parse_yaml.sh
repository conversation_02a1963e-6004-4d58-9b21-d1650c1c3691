#!/bin/bash

# parse_yaml.sh - 通用YAML解析脚本
# 
# 这个脚本提供了一个强大的YAML解析函数，支持：
# - 嵌套结构解析（节和子键）
# - 注释处理（行内注释和完整注释行）
# - 空格和引号处理
# - 调试日志（可选）
#
# 使用方法：
# 1. 在其他脚本中 source 这个文件：source validation/parse_yaml.sh
# 2. 调用 parse_yaml 函数：parse_yaml "config.yaml" "PREFIX"
# 3. 使用解析后的变量：echo $PREFIX_key_name

# 改进的 YAML 解析函数 - 支持嵌套结构
parse_yaml() {
    local file=$1
    local prefix=$2
    local debug=${3:-false}  # 可选的调试参数
    local current_section=""

    # 检查文件是否存在
    if [ ! -f "$file" ]; then
        if [ "$debug" = true ]; then
            echo "[DEBUG] YAML文件不存在: $file" >&2
        fi
        return 1
    fi

    if [ "$debug" = true ]; then
        echo "[DEBUG] 解析YAML文件: $file (前缀: $prefix)" >&2
    fi

    while IFS= read -r line; do
        # 跳过注释和空行
        if [[ $line =~ ^[[:space:]]*# || $line =~ ^[[:space:]]*$ ]]; then
            continue
        fi

        # 检测顶级节（无缩进的键）
        if [[ $line =~ ^([^[:space:]]+):[[:space:]]*(.*)$ ]]; then
            key="${BASH_REMATCH[1]}"
            value="${BASH_REMATCH[2]}"

            # 移除注释部分（# 后面的内容）
            value="${value%%#*}"
            # 移除尾部空格
            value="${value%"${value##*[![:space:]]}"}"
            
            # 移除引号
            value="${value#\"}"
            value="${value%\"}"

            # 如果值为空，这是一个节标题
            if [[ -z "$value" || "$value" == "null" ]]; then
                current_section="$key"
                if [ "$debug" = true ]; then
                    echo "[DEBUG] 进入节: $current_section" >&2
                fi
            else
                # 这是一个顶级键值对
                declare -g "${prefix}_${key}=$value"
                if [ "$debug" = true ]; then
                    echo "[DEBUG] 顶级配置: ${prefix}_${key}=$value" >&2
                fi
                current_section=""
            fi
        # 检测缩进的键（属于某个节）
        elif [[ $line =~ ^[[:space:]]+([^:]+):[[:space:]]*(.*)$ ]]; then
            key="${BASH_REMATCH[1]// /}"  # 移除键中的空格
            value="${BASH_REMATCH[2]}"

            # 移除注释部分（# 后面的内容）
            value="${value%%#*}"
            # 移除尾部空格
            value="${value%"${value##*[![:space:]]}"}"
            
            # 移除引号
            value="${value#\"}"
            value="${value%\"}"

            # 只处理非空和非null值
            if [[ "$value" != "null" && "$value" != "" ]]; then
                if [[ -n "$current_section" ]]; then
                    declare -g "${prefix}_${current_section}_${key}=$value"
                    if [ "$debug" = true ]; then
                        echo "[DEBUG] 节配置: ${prefix}_${current_section}_${key}=$value" >&2
                    fi
                else
                    declare -g "${prefix}_${key}=$value"
                    if [ "$debug" = true ]; then
                        echo "[DEBUG] 缩进配置: ${prefix}_${key}=$value" >&2
                    fi
                fi
            fi
        fi
    done < "$file"

    if [ "$debug" = true ]; then
        echo "[DEBUG] YAML解析完成: $file" >&2
    fi

    return 0
}

# 简化版YAML解析函数 - 只处理简单键值对（兼容旧版本）
parse_yaml_simple() {
    local file=$1
    local prefix=$2
    local debug=${3:-false}

    # 检查文件是否存在
    if [ ! -f "$file" ]; then
        if [ "$debug" = true ]; then
            echo "[DEBUG] YAML文件不存在: $file" >&2
        fi
        return 1
    fi

    if [ "$debug" = true ]; then
        echo "[DEBUG] 简单解析YAML文件: $file (前缀: $prefix)" >&2
    fi

    while IFS= read -r line; do
        # 跳过注释和空行
        if [[ $line =~ ^[[:space:]]*# || $line =~ ^[[:space:]]*$ ]]; then
            continue
        fi
        
        # 只处理简单的键值对
        if [[ $line =~ ^[[:space:]]*([^:]+):[[:space:]]*(.*)$ ]]; then
            key="${BASH_REMATCH[1]// /}"  # 移除键中的空格
            value="${BASH_REMATCH[2]}"
            
            # 移除注释部分
            value="${value%%#*}"
            # 移除尾部空格
            value="${value%"${value##*[![:space:]]}"}"
            
            # 移除引号
            value="${value#\"}"
            value="${value%\"}"
            
            # 只处理非空和非null值
            if [[ "$value" != "null" && "$value" != "" ]]; then
                declare -g "${prefix}_${key}=$value"
                if [ "$debug" = true ]; then
                    echo "[DEBUG] 简单配置: ${prefix}_${key}=$value" >&2
                fi
            fi
        fi
    done < "$file"

    if [ "$debug" = true ]; then
        echo "[DEBUG] 简单YAML解析完成: $file" >&2
    fi

    return 0
}

# 检查YAML解析结果的辅助函数
check_yaml_vars() {
    local prefix=$1
    local required_vars=("${@:2}")  # 从第二个参数开始的所有参数
    local missing_vars=()

    for var in "${required_vars[@]}"; do
        local var_name="${prefix}_${var}"
        if [ -z "${!var_name}" ]; then
            missing_vars+=("$var_name")
        fi
    done

    if [ ${#missing_vars[@]} -gt 0 ]; then
        echo "[ERROR] 缺少必需的配置变量:" >&2
        for var in "${missing_vars[@]}"; do
            echo "  - $var" >&2
        done
        return 1
    fi

    return 0
}

# 显示所有解析的变量（调试用）
show_yaml_vars() {
    local prefix=$1
    echo "[DEBUG] 显示所有 ${prefix}_ 开头的变量:" >&2
    env | grep "^${prefix}_" | sort >&2
}

# 如果直接运行此脚本，显示使用说明
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    echo "parse_yaml.sh - 通用YAML解析脚本"
    echo ""
    echo "使用方法："
    echo "  source validation/parse_yaml.sh"
    echo "  parse_yaml \"config.yaml\" \"PREFIX\" [debug]"
    echo ""
    echo "示例："
    echo "  source validation/parse_yaml.sh"
    echo "  parse_yaml \"validation/configs/config_shared.yaml\" \"SHARED\" true"
    echo "  echo \$SHARED_model_name"
    echo ""
    echo "函数："
    echo "  parse_yaml        - 完整YAML解析（支持嵌套结构）"
    echo "  parse_yaml_simple - 简单YAML解析（只支持键值对）"
    echo "  check_yaml_vars   - 检查必需变量是否存在"
    echo "  show_yaml_vars    - 显示所有解析的变量"
fi
