# 配置文件修改总结

## 已完成的修改

### 1. 配置文件结构调整 ✅

**修改原因：** `trace_file` 参数不是共享参数，因为 vidur 和 vLLM 使用不同格式的数据文件。

**修改内容：**
- **`config_shared.yaml`**: 移除了 `trace_file` 参数
- **`config_vidur.yaml`**: 添加了 `trace_file: "PLACEHOLDER_VIDUR_TRACE_FILE"` 占位符
- **`config_vllm.yaml`**: 添加了 `test_dataset: "PLACEHOLDER_VLLM_DATASET"` 占位符

### 2. 简化的数据生成工具 ✅

**`validation/generate_vllm_test_data.py`** - 优化版本：
- 生成1000条全英文高质量 prompts，避免乱码问题
- 使用英文模板和主题，确保内容可读性
- 生成适合 vLLM 直接调用的 JSON 格式数据
- 自动创建简单的 vLLM 测试脚本
- **不再手动计算** `arrived_at`、`num_prefill_tokens`、`num_decode_tokens`
- 这些数据将在 vLLM 运行时自动获取和记录

## vLLM 内置数据集

### 推荐用于 Demo 测试：

1. **`random` 数据集** (最适合)
   - 完全合成数据，无需外部文件
   - 可控制输入/输出长度
   - 通过 `generate_vllm_test_data.py` 自动生成

2. **HuggingFace 数据集**：
   - `philschmid/mt-bench`
   - `likaixin/InstructCoder`
   - `AI-MO/aimo-validation-aime`

## 实验流程

### 简化的两阶段流程：

```bash
# 阶段 1: 生成数据并运行 vLLM
python validation/generate_vllm_test_data.py --num-requests 1000
python validation/verify_prompts.py  # 验证数据质量
python validation/test_data/run_vllm_test.py

# 阶段 2: 更新配置并运行 Vidur
# 手动更新 config_vidur.yaml 中的 trace_file 路径
./validation/run_vidur.sh
```

## 生成的文件示例

运行 `python validation/generate_vllm_test_data.py --num-requests 1000` 后：

```
validation/test_data/
├── prompts_20250806_103014.json     # 1000条全英文 prompts
└── run_vllm_test.py                 # vLLM 测试脚本
```

**数据质量验证结果：**
- ✅ 1000条 prompts，100% 英文内容
- ✅ 平均长度 68.1 词，长度分布均匀
- ✅ 无重复内容，涵盖多个技术主题
- ✅ 无乱码或特殊字符问题

运行 vLLM 测试后将生成：
```
validation/test_data/
└── vllm_results_20250806_HHMMSS.csv # 包含实际运行数据的 CSV
```

## 关键改进

1. **高质量英文数据**：生成1000条全英文 prompts，避免乱码问题
2. **简化数据生成**：只生成 prompts，不预计算运行时数据
3. **自动化程度更高**：一键生成测试脚本和数据验证
4. **数据格式正确**：vLLM 运行时自动获取准确的 token 数量和时间戳
5. **易于使用**：减少手动操作步骤
6. **质量保证**：内置数据验证工具确保数据质量

## 下一步

1. 运行生成的 vLLM 测试脚本
2. 将生成的 CSV 文件路径更新到 `config_vidur.yaml`
3. 运行 Vidur 模拟进行对比
4. 分析两者的性能差异
