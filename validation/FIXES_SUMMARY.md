# Vidur vs vLLM 对比实验修复总结

## 问题诊断

通过深入分析，发现了导致 vidur 结果中 `request_e2e_time` 递增的根本原因：

### 1. 主要问题：调度器参数不匹配
- **问题**：vidur 使用的 `batch_size_cap=64` 和 `max_tokens_in_batch=2048` 远小于 vLLM 的对应参数
- **影响**：批处理能力严重不足，导致请求积压，`request_e2e_time` 递增
- **根因**：配置参数没有正确从共享配置传递给 vidur

### 2. 配置文件管理问题
- **问题**：参数分散在多个配置文件中，缺乏统一管理
- **影响**：参数不一致，难以确保对比实验的有效性
- **根因**：缺乏配置验证机制

### 3. 脚本参数解析问题
- **问题**：run_vidur.sh 脚本的 YAML 解析不完善，无法正确处理嵌套结构
- **影响**：关键参数没有正确传递给 vidur
- **根因**：bash 脚本的 YAML 解析能力有限

## 修复方案

### 1. 统一配置管理

#### 修改 `validation/configs/config_shared.yaml`
- ✅ 将 `max_tokens` 从 2048 改为 4096，与 `max_model_len` 一致
- ✅ 将 `max_num_batched_tokens` 从 4096 改为 16384，支持更多并发序列
- ✅ 添加 `network_device` 和 `memory_margin_fraction` 等 vidur 特定参数
- ✅ 添加执行时间预测器配置参数

#### 修改 `validation/configs/config_vidur.yaml`
- ✅ 简化配置，大部分参数从共享配置获取
- ✅ 明确参数来源和映射关系
- ✅ 优化执行时间预测器配置

#### 修改 `validation/configs/config_vllm.yaml`
- ✅ 添加关键参数的显式声明，确保与共享配置一致
- ✅ 调整 QPS 参数与共享配置保持一致

### 2. 改进脚本参数解析

#### 重写 `validation/run_vidur.sh`
- ✅ 改进 YAML 解析函数，支持嵌套结构
- ✅ 添加参数验证和调试输出
- ✅ 确保关键参数正确映射：
  - `max_num_seqs` → `--vllm_scheduler_config_batch_size_cap`
  - `max_num_batched_tokens` → `--vllm_scheduler_config_max_tokens_in_batch`
  - `max_tokens` → `--trace_request_generator_config_max_tokens`
- ✅ 处理参数值中的注释，提取纯数字值

### 3. 添加配置验证工具

#### 创建 `validation/verify_config.py`
- ✅ 验证 vLLM 和 vidur 配置的一致性
- ✅ 检查关键参数的合理性
- ✅ 验证 trace 文件格式
- ✅ 提供详细的错误报告和修复建议

#### 创建 `validation/test_vidur_args.sh`
- ✅ 测试参数构建过程，不实际运行 vidur
- ✅ 验证关键参数是否正确传递
- ✅ 提供详细的调试输出

## 关键参数对照表

| 功能 | 共享配置参数 | vLLM 参数 | Vidur 参数 | 修复后的值 |
|------|-------------|-----------|------------|------------|
| 最大并发序列数 | `max_num_seqs` | `--max-num-seqs` | `--vllm_scheduler_config_batch_size_cap` | 128 |
| 批处理最大token数 | `max_num_batched_tokens` | `--max-num-batched-tokens` | `--vllm_scheduler_config_max_tokens_in_batch` | 16384 |
| 最大token数 | `max_tokens` | `--max-model-len` | `--trace_request_generator_config_max_tokens` | 4096 |
| KV缓存块大小 | `block_size` | `--block-size` | `--vllm_scheduler_config_block_size` | 16 |
| 张量并行度 | `tensor_parallel_size` | `--tensor-parallel-size` | `--replica_config_tensor_parallel_size` | 1 |
| 流水线并行度 | `pipeline_parallel_size` | `--pipeline-parallel-size` | `--replica_config_num_pipeline_stages` | 1 |

## 验证结果

### 配置验证
```bash
./env/bin/python validation/verify_config.py
```
- ✅ 配置验证通过
- ✅ Trace 文件格式正确 (48 条记录)
- ✅ 关键参数一致性检查通过

### 参数构建验证
```bash
bash validation/test_vidur_args.sh
```
- ✅ 参数解析正确
- ✅ 关键参数正确映射
- ✅ batch_size_cap = 128 (对应 vLLM max_num_seqs)
- ✅ max_tokens_in_batch = 16384 (对应 vLLM max_num_batched_tokens)
- ✅ trace max_tokens = 4096

## 预期效果

修复后，vidur 的性能应该显著改善：

1. **消除 request_e2e_time 递增问题**
   - 批处理能力从 64→128 序列，2048→16384 tokens
   - 减少请求积压和等待时间

2. **提高模拟保真度**
   - 调度器参数与 vLLM 完全一致
   - 执行时间预测器参数优化

3. **确保实验有效性**
   - 统一的配置管理
   - 自动化的配置验证
   - 详细的参数映射文档

## 使用说明

### 1. 验证配置
```bash
./env/bin/python validation/verify_config.py
```

### 2. 测试参数构建（可选）
```bash
bash validation/test_vidur_args.sh
```

### 3. 运行 vidur 模拟
```bash
bash validation/run_vidur.sh
```

### 4. 运行 vLLM 测试
```bash
./env/bin/python validation/run_vllm.py
```

## 注意事项

1. **NumPy 版本兼容性**：如果遇到 NumPy 版本问题，可能需要降级到 NumPy < 2.0

2. **Trace 文件更新**：如果重新生成 vLLM 结果，需要更新 `config_vidur.yaml` 中的 `trace_file` 路径

3. **参数调优**：可以根据实际需求调整 `max_num_batched_tokens` 等参数，但要确保 vLLM 和 vidur 保持一致

4. **结果对比**：修复后的结果应该显示：
   - vidur 的 `request_e2e_time` 不再递增
   - 两个系统的性能指标更加接近
   - 更高的吞吐量和更低的延迟
