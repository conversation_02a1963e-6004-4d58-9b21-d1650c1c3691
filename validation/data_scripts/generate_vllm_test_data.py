#!/usr/bin/env python3
"""
vLLM 测试数据生成脚本
生成适合 vLLM 直接调用的随机数据集
"""

import argparse
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

import yaml
from vllm.benchmarks.datasets import RandomDataset
from vllm.transformers_utils.tokenizer import get_tokenizer


def load_config(config_path: str) -> Dict[str, Any]:
    """加载 YAML 配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def merge_configs(shared_config: Dict[str, Any], specific_config: Dict[str, Any]) -> Dict[str, Any]:
    """合并共享配置和特定配置"""
    merged = shared_config.copy()
    merged.update(specific_config)
    return merged


def generate_english_prompts(tokenizer, num_requests: int = 100, input_len: int = 512, output_len: int = 128) -> List[str]:
    """生成英文随机 prompts 列表"""
    print(f"生成英文随机数据集: {num_requests} 个请求, 输入长度={input_len}, 输出长度={output_len}")

    # 使用英文词汇生成更清晰的 prompts
    english_templates = [
        "Write a detailed explanation about",
        "Describe the process of",
        "Explain how to",
        "What are the benefits of",
        "Compare and contrast",
        "Analyze the impact of",
        "Discuss the importance of",
        "Provide an overview of",
        "Summarize the key points about",
        "Evaluate the effectiveness of"
    ]

    english_topics = [
        "artificial intelligence in healthcare",
        "renewable energy technologies",
        "machine learning algorithms",
        "sustainable development practices",
        "digital transformation strategies",
        "cloud computing architectures",
        "data science methodologies",
        "cybersecurity best practices",
        "software engineering principles",
        "project management frameworks",
        "business intelligence systems",
        "user experience design",
        "mobile application development",
        "database optimization techniques",
        "network security protocols",
        "agile development methodologies",
        "quality assurance processes",
        "system integration approaches",
        "performance monitoring tools",
        "automated testing strategies"
    ]

    import random
    random.seed(42)  # 确保可重现性

    prompts = []
    for i in range(num_requests):
        template = random.choice(english_templates)
        topic = random.choice(english_topics)

        # 创建基础 prompt
        base_prompt = f"{template} {topic}."

        # 根据需要的长度扩展 prompt
        current_tokens = len(tokenizer.encode(base_prompt))
        target_tokens = input_len + random.randint(-int(input_len * 0.1), int(input_len * 0.1))

        if current_tokens < target_tokens:
            # 添加更多内容来达到目标长度
            extensions = [
                " Please provide specific examples and detailed explanations.",
                " Include relevant case studies and practical applications.",
                " Discuss both advantages and potential challenges.",
                " Consider different perspectives and approaches.",
                " Explain the technical details and implementation considerations.",
                " Address common misconceptions and provide clarifications.",
                " Include historical context and future trends.",
                " Provide step-by-step instructions where applicable.",
                " Discuss best practices and industry standards.",
                " Consider the economic and social implications."
            ]

            extended_prompt = base_prompt
            while len(tokenizer.encode(extended_prompt)) < target_tokens and extensions:
                extension = random.choice(extensions)
                extensions.remove(extension)  # 避免重复
                extended_prompt += extension

            prompts.append(extended_prompt)
        else:
            prompts.append(base_prompt)

    return prompts


def save_prompts_json(prompts: List[str], output_path: str):
    """保存 prompts 为 JSON 格式"""
    print(f"保存 prompts 到: {output_path}")

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(prompts, f, indent=2, ensure_ascii=False)


def create_simple_vllm_script(prompts_path: str, output_dir: str, output_len: int = 128) -> str:
    """创建简单的 vLLM 测试脚本"""
    script_content = f'''#!/usr/bin/env python3
"""
简单的 vLLM 测试脚本
加载 prompts 并运行推理，输出结果供 Vidur 对比使用
"""

import json
import time
import csv
from datetime import datetime
from vllm import LLM, SamplingParams

def main():
    # 加载 prompts
    with open("{prompts_path}", "r", encoding="utf-8") as f:
        prompts = json.load(f)

    print(f"加载了 {{len(prompts)}} 个 prompts")

    # 初始化 vLLM（使用配置文件中的参数）
    llm = LLM(
        model="/share_data/llm_weights/Meta-Llama-3-8B",
        tensor_parallel_size=1,
        max_model_len=4096,
        gpu_memory_utilization=0.9,
        block_size=16,
        max_num_seqs=128,
        max_num_batched_tokens=4096,
        seed=42,
        trust_remote_code=False,
    )

    # 设置采样参数
    sampling_params = SamplingParams(
        max_tokens={output_len},
        temperature=0.0,  # 确定性输出
        top_p=1.0,
        ignore_eos=True,
    )

    print("开始推理...")

    # 记录开始时间
    start_time = time.time()

    # 执行推理
    outputs = llm.generate(prompts, sampling_params, use_tqdm=True)

    # 记录结束时间
    end_time = time.time()

    # 收集结果并保存为 Vidur 可用的格式
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    csv_path = f"{output_dir}/vllm_results_{{timestamp}}.csv"

    with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['request_id', 'arrived_at', 'num_prefill_tokens', 'num_decode_tokens']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()

        for i, (prompt, output) in enumerate(zip(prompts, outputs)):
            # 计算实际的 token 数量
            prompt_tokens = len(llm.get_tokenizer().encode(prompt))
            decode_tokens = len(output.outputs[0].token_ids)

            writer.writerow({{
                'request_id': i,
                'arrived_at': start_time + i * 0.01,  # 假设请求间隔 0.01 秒
                'num_prefill_tokens': prompt_tokens,
                'num_decode_tokens': decode_tokens,
            }})

    # 打印统计信息
    total_time = end_time - start_time
    total_prompts = len(prompts)
    total_tokens = sum(len(output.outputs[0].token_ids) for output in outputs)
    throughput = total_tokens / total_time

    print(f"\\n=== vLLM 测试完成 ===")
    print(f"总请求数: {{total_prompts}}")
    print(f"总时间: {{total_time:.2f}} 秒")
    print(f"总输出令牌数: {{total_tokens}}")
    print(f"吞吐量: {{throughput:.2f}} tokens/sec")
    print(f"结果已保存到: {{csv_path}}")
    print(f"\\n请将此 CSV 文件路径更新到 config_vidur.yaml 的 trace_file 字段中")

if __name__ == "__main__":
    main()
'''

    script_path = f"{output_dir}/run_vllm_test.py"
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)

    # 添加执行权限
    import os
    os.chmod(script_path, 0o755)

    return script_path


def main():
    parser = argparse.ArgumentParser(description="生成 vLLM 测试数据")
    parser.add_argument("--num-requests", type=int, default=1000,
                       help="生成的请求数量")
    parser.add_argument("--input-len", type=int, default=512,
                       help="输入长度")
    parser.add_argument("--output-len", type=int, default=128,
                       help="输出长度")
    parser.add_argument("--output-dir", default="validation/test_data",
                       help="输出目录")

    args = parser.parse_args()

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)

    print("=== 生成 vLLM 测试数据 ===")
    print(f"请求数量: {args.num_requests}")
    print(f"输入长度: {args.input_len}")
    print(f"输出长度: {args.output_len}")

    # 初始化 tokenizer（使用本地模型路径）
    tokenizer = get_tokenizer("/share_data/llm_weights/Meta-Llama-3-8B")

    # 生成英文随机 prompts
    prompts = generate_english_prompts(
        tokenizer=tokenizer,
        num_requests=args.num_requests,
        input_len=args.input_len,
        output_len=args.output_len
    )

    # 保存 prompts
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    prompts_path = output_dir / f"prompts_{timestamp}.json"

    save_prompts_json(prompts, str(prompts_path))

    # 创建简单的 vLLM 测试脚本
    script_path = create_simple_vllm_script(str(prompts_path), str(output_dir), args.output_len)

    print(f"\n=== 生成完成 ===")
    print(f"Prompts 文件: {prompts_path}")
    print(f"vLLM 测试脚本: {script_path}")
    print(f"\n下一步:")
    print(f"1. 运行 vLLM 测试: python {script_path}")
    print(f"2. 将生成的 CSV 文件路径更新到 config_vidur.yaml 的 trace_file 字段")
    print(f"3. 运行 Vidur 模拟进行对比")


if __name__ == "__main__":
    main()
