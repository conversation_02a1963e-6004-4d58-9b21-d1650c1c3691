#!/usr/bin/env python3
"""
过滤 CSV/JSON 文件中指定列/键的词语数量在指定范围内的脚本

此脚本可以读取 CSV 或 JSON 文件，过滤保留指定列/键中词语数量在指定范围内的行/条目，
只保留文本内容，并将结果保存到新的 JSON 文件中。
"""

import pandas as pd
import argparse
import os
import json
from pathlib import Path


def count_words(text):
    """
    计算文本中的词语数量
    
    Args:
        text (str): 输入文本
        
    Returns:
        int: 词语数量
    """
    if pd.isna(text) or text == '':
        return 0
    
    # 简单的词语计数，按空格分割
    words = str(text).split()
    return len(words)


def filter_file_by_word_count(input_file, output_file=None, min_words=1000, max_words=3000, 
                             column_or_key='Articles', file_type=None):
    """
    过滤 CSV/JSON 文件中指定列/键的词语数量在指定范围内的行/条目，只保留文本内容
    
    Args:
        input_file (str): 输入文件路径 (CSV 或 JSON)
        output_file (str, optional): 输出 JSON 文件路径。如果为 None，自动生成
        min_words (int): 最小词语数量阈值，默认 1000
        max_words (int): 最大词语数量阈值，默认 3000
        column_or_key (str): CSV文件的列名或JSON文件的键名，默认 'Articles'
        file_type (str, optional): 文件类型 ('csv' 或 'json')。如果为 None，根据文件扩展名判断
        
    Returns:
        str: 输出文件路径
    """
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")
    
    # 判断文件类型
    if file_type is None:
        file_type = 'csv' if input_file.lower().endswith('.csv') else 'json'
    
    print(f"正在读取 {file_type.upper()} 文件: {input_file}")
    
    if file_type == 'csv':
        # 读取 CSV 文件
        try:
            df = pd.read_csv(input_file)
        except Exception as e:
            raise ValueError(f"读取 CSV 文件失败: {e}")
        
        print(f"原始数据集包含 {len(df)} 行")
        
        # 检查是否存在指定列
        if column_or_key not in df.columns:
            raise ValueError(f"CSV 文件中没有找到 '{column_or_key}' 列")
        
        # 计算每行指定列的词语数量
        print("正在计算词语数量...")
        df['word_count'] = df[column_or_key].apply(count_words)
        
        # 显示词语数量统计信息
        print(f"\n词语数量统计:")
        print(f"最小词语数: {df['word_count'].min()}")
        print(f"最大词语数: {df['word_count'].max()}")
        print(f"平均词语数: {df['word_count'].mean():.2f}")
        print(f"中位数词语数: {df['word_count'].median():.2f}")
        
        # 过滤词语数量在指定范围内的行
        filtered_df = df[(df['word_count'] >= min_words) & (df['word_count'] < max_words)].copy()
        
        print(f"\n过滤条件: {min_words} <= {column_or_key} 列词语数量 < {max_words}")
        print(f"过滤后数据集包含 {len(filtered_df)} 行")
        print(f"过滤掉了 {len(df) - len(filtered_df)} 行")
        print(f"保留比例: {len(filtered_df) / len(df) * 100:.2f}%")
        
        # 只保留指定列的文本内容
        articles_list = filtered_df[column_or_key].tolist()
        
    else:  # JSON 文件
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except Exception as e:
            raise ValueError(f"读取 JSON 文件失败: {e}")
        
        if isinstance(data, list):
            print(f"原始数据集包含 {len(data)} 条记录")
            
            # 检查是否为字符串列表（如现有的JSON文件格式）
            if all(isinstance(item, str) for item in data):
                # 如果是字符串列表，直接处理每个字符串
                word_counts = [count_words(text) for text in data]
                
                print(f"\n词语数量统计:")
                print(f"最小词语数: {min(word_counts)}")
                print(f"最大词语数: {max(word_counts)}")
                print(f"平均词语数: {sum(word_counts)/len(word_counts):.2f}")
                
                # 过滤词语数量在指定范围内的文本
                filtered_texts = [text for text, wc in zip(data, word_counts) 
                                if min_words <= wc < max_words]
                
                print(f"\n过滤条件: {min_words} <= 文本词语数量 < {max_words}")
                print(f"过滤后数据集包含 {len(filtered_texts)} 条记录")
                print(f"过滤掉了 {len(data) - len(filtered_texts)} 条记录")
                print(f"保留比例: {len(filtered_texts) / len(data) * 100:.2f}%")
                
                articles_list = filtered_texts
                
            else:
                # 如果是对象列表，检查指定键
                if not all(isinstance(item, dict) and column_or_key in item for item in data):
                    raise ValueError(f"JSON 文件中的某些记录没有找到 '{column_or_key}' 键")
                
                # 计算每条记录指定键的词语数量
                print("正在计算词语数量...")
                word_counts = [count_words(item[column_or_key]) for item in data]
                
                print(f"\n词语数量统计:")
                print(f"最小词语数: {min(word_counts)}")
                print(f"最大词语数: {max(word_counts)}")
                print(f"平均词语数: {sum(word_counts)/len(word_counts):.2f}")
                
                # 过滤词语数量在指定范围内的记录
                filtered_data = [item for item, wc in zip(data, word_counts) 
                               if min_words <= wc < max_words]
                
                print(f"\n过滤条件: {min_words} <= {column_or_key} 键词语数量 < {max_words}")
                print(f"过滤后数据集包含 {len(filtered_data)} 条记录")
                print(f"过滤掉了 {len(data) - len(filtered_data)} 条记录")
                print(f"保留比例: {len(filtered_data) / len(data) * 100:.2f}%")
                
                # 只保留指定键的文本内容
                articles_list = [item[column_or_key] for item in filtered_data]
        else:
            raise ValueError("JSON 文件格式不支持，需要是数组格式")
    
    
    # 生成输出文件名
    if output_file is None:
        input_path = Path(input_file)
        output_file = input_path.parent / f"{input_path.stem}_filtered_{min_words}_to_{max_words}_words.json"
    
    # 保存过滤后的数据为 JSON 格式，只包含文本内容
    print(f"\n正在保存到: {output_file}")
    try:
        # 只保存文本列表
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(articles_list, f, indent=2, ensure_ascii=False)
        print("保存成功!")
    except Exception as e:
        raise ValueError(f"保存文件失败: {e}")
    
    return str(output_file)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="过滤 CSV/JSON 文件中指定列/键的词语数量在指定范围内的行/条目，只保留文本内容",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 过滤 CSV 文件的 Articles 列
  python filter_articles_by_word_count.py ../datasets/bbc-news-summary.csv
  
  # 过滤 CSV 文件的自定义列
  python filter_articles_by_word_count.py ../datasets/data.csv --column Summary
  
  # 过滤 JSON 文件（字符串数组）
  python filter_articles_by_word_count.py ../datasets/articles.json
  
  # 过滤 JSON 文件的指定键
  python filter_articles_by_word_count.py ../datasets/data.json --key content
  
  # 指定输出文件和词数范围
  python filter_articles_by_word_count.py input.csv -o filtered.json --min-words 2000 --max-words 4000
        """
    )
    
    parser.add_argument(
        '-i', '--input',
        dest='input_file',
        help='输入文件路径 (CSV 或 JSON)',
        default='/mnt/yrfs/users/yukaifeng/vidur/validation/datasets/podcast_summary_assessment.json'
    )
    
    parser.add_argument(
        '-o', '--output',
        dest='output_file',
        help='输出 JSON 文件路径（可选，默认自动生成）',
        default='/mnt/yrfs/users/yukaifeng/vidur/validation/datasets/podcast_4k_filtered.json'
    )
    
    parser.add_argument(
        '--min-words',
        dest='min_words',
        type=int,
        default=1000,
        help='最小词语数量阈值（默认: 1000）'
    )
    
    parser.add_argument(
        '--max-words',
        dest='max_words',
        type=int,
        default=3000,
        help='最大词语数量阈值（默认: 3000）'
    )
    
    parser.add_argument(
        '--column',
        dest='column_or_key',
        default='transcript',
        help='CSV文件的列名或JSON文件的键名（默认: Articles）'
    )
    
    parser.add_argument(
        '--key',
        dest='column_or_key',
        help='JSON文件的键名（与--column等价）'
    )
    
    parser.add_argument(
        '--type',
        dest='file_type',
        choices=['csv', 'json'],
        help='文件类型，如果不指定则根据文件扩展名判断'
    )
    
    args = parser.parse_args()
    
    try:
        output_file = filter_file_by_word_count(
            args.input_file,
            args.output_file,
            args.min_words,
            args.max_words,
            args.column_or_key,
            args.file_type
        )
        
        print(f"\n✅ 处理完成!")
        print(f"输出文件: {output_file}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
