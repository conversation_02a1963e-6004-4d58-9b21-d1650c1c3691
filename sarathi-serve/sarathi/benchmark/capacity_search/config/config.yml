schedulers:
  - name: vllm
    scheduler: vllm
    batch_size: 128
  - name: sarathi_256
    scheduler: sarathi
    chunk_size: 256
    batch_size: 128
  - name: sarathi_512
    scheduler: sarathi
    chunk_size: 512
    batch_size: 128
  - name: sarathi_1024
    scheduler: sarathi
    chunk_size: 1024
    batch_size: 128
  - name: sarathi_2048
    scheduler: sarathi
    chunk_size: 2048
    batch_size: 128
  - name: sarathi_4096
    scheduler: sarathi
    chunk_size: 4096
    batch_size: 128

traces:
  - name: chat
    trace_file: "./data/processed_traces/sharegpt_8k_filtered_stats_llama2_tokenizer.csv"
    max_seq_len: 8192
    num_requests: 2048
    start_qps: 4
  - name: arxiv
    trace_file: "./data/processed_traces/arxiv_summarization_stats_llama2_tokenizer_filtered_v2.csv"
    max_seq_len: 16384
    num_requests: 2048
    start_qps: 2

parallel_spec:
  - name: tp_1
    tp_dimension: 1
    pp_dimension: 1
  - name: tp_2
    tp_dimension: 2
    pp_dimension: 1
  - name: tp_4
    tp_dimension: 4
    pp_dimension: 1
  - name: tp_4_pp_2
    tp_dimension: 4
    pp_dimension: 2

models:
  - name: llama-2-7b-hf
    identifier: meta-llama/Llama-2-7b-hf
    parallel_specs: ["tp_1"]
    scheduler_specs: null # use all
    traces: null # use all
  - name: codellama-34b-instruct-hf
    identifier: codellama/CodeLlama-34b-Instruct-hf
    parallel_specs: ["tp_2"]
    scheduler_specs: null # use all
    traces: null # use all
  - name: llama-2-70b-hf
    identifier: meta-llama/Llama-2-70b-hf
    parallel_specs: ["tp_4"]
    scheduler_specs: null # use all
    traces: null # use all

